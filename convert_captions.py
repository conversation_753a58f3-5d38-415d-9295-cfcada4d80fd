#!/usr/bin/env python3
"""
脚本用于将generated_fmri2text_captions.json文件转换为指定格式
输入格式：包含1000个句子的JSON数组，每个句子被<start_of_text>和<end_of_text>标签包围
输出格式：字典格式，键为字符串数字，值为包含单个句子的列表
"""

import json
import re
import os

def clean_text(text):
    """
    清理文本，移除<start_of_text>和<end_of_text>标签
    """
    # 移除开始和结束标签
    cleaned = re.sub(r'<start_of_text>', '', text)
    cleaned = re.sub(r'<end_of_text>', '', cleaned)
    # 移除首尾空格
    cleaned = cleaned.strip()
    return cleaned

def convert_captions(input_file, output_file):
    """
    转换captions文件格式
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
    """
    try:
        # 读取输入文件
        print(f"正在读取文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 验证数据格式
        if not isinstance(data, list):
            raise ValueError("输入文件应该包含一个列表")
        
        print(f"找到 {len(data)} 个句子")
        
        # 转换格式
        converted_data = {}
        for i, sentence in enumerate(data):
            if isinstance(sentence, str):
                # 清理文本
                cleaned_sentence = clean_text(sentence)
                # 创建新格式：键为字符串数字，值为包含单个句子的列表
                converted_data[str(i)] = [cleaned_sentence]
            else:
                print(f"警告: 第 {i} 个元素不是字符串: {type(sentence)}")
                converted_data[str(i)] = [str(sentence)]
        
        # 保存转换后的数据
        print(f"正在保存到文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=4)
        
        print(f"转换完成！共处理了 {len(converted_data)} 个句子")
        
        # 显示前几个示例
        print("\n前3个转换示例:")
        for i in range(min(3, len(converted_data))):
            print(f'"{i}": {converted_data[str(i)]}')
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {e}")
        return False
    except Exception as e:
        print(f"错误: {e}")
        return False

def main():
    """主函数"""
    # 设置输入和输出文件路径
    input_file = "result/generated_fmri2text_captions.json"
    output_file = "result/converted_captions.json"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        print("请确保文件路径正确")
        return
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 执行转换
    success = convert_captions(input_file, output_file)
    
    if success:
        print(f"\n✅ 转换成功！输出文件: {output_file}")
    else:
        print("\n❌ 转换失败！")

if __name__ == "__main__":
    main()
